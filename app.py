from fastapi import FastAPI
import logging
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Import presentation layer
from src.presentation.controllers.media_controller import router
from src.infrastructure.config.settings import settings

# Configure logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

# Ensure required directories exist
settings.ensure_directories()

app = FastAPI(title="InstaSaver API", version="1.0.0")

# Include the API router
app.include_router(router)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app, 
        host=settings.server.host, 
        port=settings.server.port,
        reload=settings.server.reload
    )
