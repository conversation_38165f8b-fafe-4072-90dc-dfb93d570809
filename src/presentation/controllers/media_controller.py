"""Media download controller."""
import logging
from datetime import datetime
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from src.presentation.models.request_models import DownloadRequest, SearchMusicRequest, DownloadFromShortcodeRequest
from src.presentation.models.response_models import DownloadResponse, ErrorResponse, HealthResponse, SearchMusicResponse
from src.application.dtos.download_dtos import DownloadMediaRequest
from src.application.dtos.search_dtos import SearchMusicRequest as SearchMusicRequestDto, DownloadFromShortcodeRequest as DownloadFromShortcodeRequestDto
from src.presentation.dependencies import get_download_media_use_case, get_search_music_use_case, get_download_from_shortcode_use_case, get_music_cache_repository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["media"])


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        timestamp=datetime.now().isoformat()
    )


@router.post("/download", response_model=DownloadResponse)
async def download_media(request: DownloadRequest):
    """Download media from Instagram or YouTube and send to Telegram."""
    try:
        logger.info(f"Received download request for URL: {request.url}")

        # Get use case with proper dependencies
        use_case = get_download_media_use_case(request.bot_token)

        # Convert to application DTO
        app_request = DownloadMediaRequest(
            chat_id=request.chat_id,
            url=request.url,
            bot_token=request.bot_token,
            media_type=request.media_type.value,
            video_format=request.video_format
        )

        # Execute use case
        result = await use_case.execute(app_request)

        # Convert to presentation response
        if result.success:
            return DownloadResponse(
                status="success",
                message=result.message,
                file_id=result.file_id
            )
        else:
            return JSONResponse(
                status_code=400,
                content=ErrorResponse(
                    status="error",
                    message=result.message
                ).model_dump()
            )

    except ValueError as e:
        logger.error(f"Validation error: {e}")
        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Invalid request data",
                detail=str(e)
            ).model_dump()
        )
    except Exception as e:
        logger.error(f"Unexpected error in media controller: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )


@router.get("/search-music", response_model=SearchMusicResponse)
async def search_music(query: str, page: int = 1):
    """Search for music using the FastSaver API."""
    try:
        logger.info(f"Received music search request for query: {query}, page: {page}")

        # Get use case
        use_case = get_search_music_use_case()

        # Convert to application DTO
        app_request = SearchMusicRequestDto(
            query=query,
            page=page
        )

        # Execute use case
        result = await use_case.execute(app_request)

        # Convert to presentation response
        from src.presentation.models.response_models import MusicSearchResult
        search_results = [
            MusicSearchResult(
                title=item.title,
                shortcode=item.shortcode,
                duration=item.duration,
                thumb=item.thumb,
                thumb_best=item.thumb_best
            )
            for item in result.results
        ]

        return SearchMusicResponse(
            error=result.error,
            page=result.page,
            results=search_results
        )

    except ValueError as e:
        logger.error(f"Validation error: {e}")
        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Invalid request data",
                detail=str(e)
            ).model_dump()
        )
    except Exception as e:
        logger.error(f"Unexpected error in search music: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )


@router.post("/download-shortcode", response_model=DownloadResponse)
async def download_from_shortcode(request: DownloadFromShortcodeRequest):
    """Download media from YouTube shortcode and send to Telegram."""
    try:
        logger.info(f"Received download from shortcode request: {request.shortcode}")

        # Get use case
        use_case = get_download_from_shortcode_use_case(request.bot_token)

        # Convert to application DTO
        app_request = DownloadFromShortcodeRequestDto(
            chat_id=request.chat_id,
            shortcode=request.shortcode,
            bot_token=request.bot_token,
            media_type=request.media_type.value
        )

        # Execute use case
        result = await use_case.execute(app_request)

        # Convert to presentation response
        if result.success:
            return DownloadResponse(
                status="success",
                message=result.message,
                file_id=result.file_id
            )
        else:
            return JSONResponse(
                status_code=400,
                content=ErrorResponse(
                    status="error",
                    message=result.message
                ).model_dump()
            )

    except ValueError as e:
        logger.error(f"Validation error: {e}")
        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Invalid request data",
                detail=str(e)
            ).model_dump()
        )
    except Exception as e:
        logger.error(f"Unexpected error in download from shortcode: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )


@router.get("/cache-stats")
async def get_cache_stats():
    """Get music search cache statistics."""
    try:
        cache_repo = get_music_cache_repository()
        stats = await cache_repo.get_cache_stats()
        return stats
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )


@router.post("/cleanup-cache")
async def cleanup_cache():
    """Cleanup expired cache entries."""
    try:
        cache_repo = get_music_cache_repository()
        await cache_repo.cleanup_expired_cache()
        return {"status": "success", "message": "Cache cleanup completed"}
    except Exception as e:
        logger.error(f"Error cleaning up cache: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        )
